package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.data.repository.LogRepositoryImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 用于管理和暴露进度记录的单例对象。
 * 它维护一个进度记录的映射，并通过StateFlow提供响应式更新。
 */
object ProgressManager {
    // 用于存储每个账号的进度记录
    private val progressRecords = mutableMapOf<String, String>()
    private val progressRecordsLock = Any()

    // 添加一个StateFlow来包装progressRecords，用于UI更新
    private val _progressRecordsFlow = MutableStateFlow<Map<String, String>>(emptyMap())
    val progressRecordsFlow: StateFlow<Map<String, String>> = _progressRecordsFlow.asStateFlow()

    // 协程作用域，用于异步数据库操作
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    // 日志仓库，延迟初始化
    private var logRepository: LogRepositoryImpl? = null

    // 为每个token维护一个序列号计数器，确保日志顺序
    private val sequenceCounters = ConcurrentHashMap<String, AtomicLong>()

    /**
     * 获取指定token的下一个序列号
     * @param tokenUid 令牌的唯一标识符
     * @return 序列号
     */
    fun getNextSequenceNumber(tokenUid: String): Long {
        return sequenceCounters.getOrPut(tokenUid) { AtomicLong(0) }.incrementAndGet()
    }

    /**
     * 初始化日志仓库
     * @param context 应用上下文
     */
    fun initialize(context: Context) {
        if (logRepository == null) {
            try {
                val logDao = AppDatabase.getDatabase(context).logDao()
                logRepository = LogRepositoryImpl(logDao)
                Log.d("ProgressManager", "Log repository initialized successfully")
            } catch (e: Exception) {
                Log.e("ProgressManager", "Failed to initialize log repository", e)
            }
        }
    }

    /**
     * 更新给定tokenUid的进度记录。
     *
     * @param tokenUid 令牌的唯一标识符。
     * @param message 要记录的消息。
     * @param tag 日志标签，默认为"ProcessRecorder"
     * @param logLevel 日志级别，默认为"INFO"
     * @param saveToDb 是否保存到数据库，默认为true
     * @param phoneNumber 关联的手机号码，默认为空字符串
     * @param sequenceNumber 序列号，用于确保日志顺序
     */
    fun updateRecord(
        tokenUid: String,
        message: String,
        tag: String = "ProcessRecorder",
        logLevel: String = "INFO",
        saveToDb: Boolean = true,
        phoneNumber: String = "",
        sequenceNumber: Long = -1
    ) {
        val newMapSnapshot = synchronized(progressRecordsLock) {
            progressRecords[tokenUid] = message
            progressRecords.toMap()
        }
        _progressRecordsFlow.value = newMapSnapshot

        // 保存到数据库
        if (saveToDb && logRepository != null) {
            coroutineScope.launch {
                try {
                    // 使用序列号确保日志顺序
                    val timestamp = if (sequenceNumber > 0) {
                        generateOrderedTimestamp(sequenceNumber)
                    } else {
                        // 如果没有提供序列号，获取一个新的序列号
                        val newSeq = getNextSequenceNumber(tokenUid)
                        generateOrderedTimestamp(newSeq)
                    }
                    
                    logRepository?.saveLog(tokenUid, message, tag, logLevel, phoneNumber, timestamp)
                    Log.d("ProgressManager", "Log saved to database successfully")
                } catch (e: Exception) {
                    Log.e("ProgressManager", "Failed to save log to database", e)
                }
            }
        }
    }
    
    /**
     * 生成有序的时间戳，确保日志按照序列号排序
     * @param sequenceNumber 序列号
     * @return 有序的时间戳
     */
    private fun generateOrderedTimestamp(sequenceNumber: Long): Date {
        val currentTime = System.currentTimeMillis()
        // 使用当前时间的前10位，后面加上序列号的后9位（纳秒级精度）
        // 这样可以在保持时间大致正确的同时，确保顺序
        val timeBase = (currentTime / 1000) * 1000 // 秒级时间戳
        return Date(timeBase + (sequenceNumber % 1000))
    }

    // 移除不再使用的timestampCounter和generateUniqueTimestamp方法

    /**
     * 从数据库同步最新的记录到进度记录映射中
     * 仅当progressRecords为空时才进行同步，避免覆盖已有的记录
     * @param orderTokens 需要同步的令牌列表
     * @param batchSize 每批处理的令牌数量，默认为50
     * @param timeoutPerBatch 每批处理的超时时间（毫秒），默认为5000ms
     * @param delayBetweenBatches 批次之间的延迟（毫秒），默认为100ms
     * @param singleQueryTimeout 单个查询的超时时间（毫秒），默认为1000ms
     */
    fun syncLatestRecordFromDbByOrderTokens(
        context: Context,
        orderTokens: List<OrderTokenEntity>,
        batchSize: Int = 50,
        timeoutPerBatch: Long = 5000,
        delayBetweenBatches: Long = 100,
        singleQueryTimeout: Long = 1000
    ) {
        // 检查progressRecords是否为空
        val isEmpty = synchronized(progressRecordsLock) {
            progressRecords.isEmpty()
        }

        // 如果progressRecords为空，则初始化日志仓库
        if (isEmpty && logRepository == null) {
            initialize(context)
        }

        // 如果progressRecords为空，才进行同步
        if (isEmpty && logRepository != null) {
            coroutineScope.launch {
                try {
                    Log.d("ProgressManager", "Syncing latest records from database for ${orderTokens.size} tokens")

                    // 检查数据库状态
                    val dbHealthy = checkDatabaseHealth(context)
                    if (!dbHealthy) {
                        Log.w("ProgressManager", "Database health check failed, using fallback strategy")
                        handleDatabaseUnavailable(orderTokens)
                        return@launch
                    }

                    val allSyncedRecords = mutableMapOf<String, String>()

                    // 批处理参数
                    val totalTokens = orderTokens.size
                    var processedCount = 0
                    var errorCount = 0
                    var timeoutCount = 0

                    // 分批处理令牌
                    val batches = orderTokens.chunked(batchSize)
                    var shouldStop = false

                    for (batchIndex in batches.indices) {
                        if (shouldStop) break

                        val tokenBatch = batches[batchIndex]
                        try {
                            // 每批次的临时记录
                            val batchRecords = mutableMapOf<String, String>()

                            // 使用withTimeout确保每批处理不会超时
                            kotlinx.coroutines.withTimeoutOrNull(timeoutPerBatch) {
                                // 处理当前批次
                                tokenBatch.forEach { orderToken ->
                                    try {
                                        // 为单个查询添加超时保护
                                        val latestRecord = kotlinx.coroutines.withTimeoutOrNull(singleQueryTimeout) {
                                            logRepository?.getLatestLogByToken(orderToken.uid)
                                        }

                                        if (latestRecord != null) {
                                            batchRecords[orderToken.uid] = latestRecord.message
                                            allSyncedRecords[orderToken.uid] = latestRecord.message
                                            Log.d("ProgressManager", "Synced record for token ${orderToken.uid}: ${latestRecord.message}")
                                        } else {
                                            // 查询超时或返回null，记录但不中断处理
                                            timeoutCount++
                                            Log.w("ProgressManager", "Query timeout or null result for token ${orderToken.uid}")
                                        }
                                    } catch (e: Exception) {
                                        errorCount++
                                        Log.e("ProgressManager", "Error syncing record for token ${orderToken.uid}", e)

                                        // 如果是数据库相关异常，考虑降级处理
                                        if (isDatabaseException(e)) {
                                            Log.w("ProgressManager", "Database exception detected, may need fallback strategy")
                                        }
                                    }
                                }
                            } ?: run {
                                // 批次超时处理
                                timeoutCount += tokenBatch.size
                                Log.w("ProgressManager", "Batch ${batchIndex + 1} processing timed out after ${timeoutPerBatch}ms")
                            }

                            processedCount += tokenBatch.size
                            Log.d("ProgressManager", "Processed batch ${batchIndex + 1}: ${tokenBatch.size} tokens, total progress: $processedCount/$totalTokens")

                            // 每批处理完成后更新progressRecords和StateFlow
                            if (batchRecords.isNotEmpty()) {
                                val newMapSnapshot = synchronized(progressRecordsLock) {
                                    progressRecords.putAll(batchRecords)
                                    progressRecords.toMap()
                                }
                                _progressRecordsFlow.value = newMapSnapshot
                            }

                            // 添加延迟，避免过度消耗系统资源
                            if (batchIndex < batches.size - 1) {
                                kotlinx.coroutines.delay(delayBetweenBatches)
                            }

                            // 如果错误率过高，考虑中断处理
                            val errorRate = errorCount.toDouble() / processedCount
                            if (errorRate > 0.5 && processedCount > 20) {
                                Log.w("ProgressManager", "High error rate detected ($errorRate), stopping sync process")
                                shouldStop = true
                            }
                        } catch (e: Exception) {
                            Log.e("ProgressManager", "Error processing batch ${batchIndex + 1}", e)
                        }
                    }

                    Log.d("ProgressManager", "Synced ${allSyncedRecords.size} records from database with $errorCount errors and $timeoutCount timeouts")

                    // 如果同步成功率很低，触发数据库维护
                    val successRate = allSyncedRecords.size.toDouble() / totalTokens
                    if (successRate < 0.3 && totalTokens > 10) {
                        Log.w("ProgressManager", "Low sync success rate ($successRate), triggering database maintenance")
                        triggerDatabaseMaintenance(context)
                    }
                } catch (e: Exception) {
                    Log.e("ProgressManager", "Failed to sync records from database", e)
                    // 发生严重错误时，尝试降级处理
                    handleDatabaseUnavailable(orderTokens)
                }
            }
        } else {
            Log.d("ProgressManager", "Skipping database sync as records are not empty or repository is null")
        }
    }

    /**
     * 同步新导入账号的进度记录
     * 只同步那些在progressRecords中不存在的账号
     * @param context 上下文
     * @param orderTokens 需要同步的令牌列表
     * @param batchSize 每批处理的令牌数量，默认为50
     * @param timeoutPerBatch 每批处理的超时时间（毫秒），默认为5000ms
     * @param delayBetweenBatches 批次之间的延迟（毫秒），默认为100ms
     * @param singleQueryTimeout 单个查询的超时时间（毫秒），默认为1000ms
     */
    fun syncNewAccountsProgress(
        context: Context,
        orderTokens: List<OrderTokenEntity>,
        batchSize: Int = 50,
        timeoutPerBatch: Long = 5000,
        delayBetweenBatches: Long = 100,
        singleQueryTimeout: Long = 1000
    ) {
        // 初始化日志仓库（如果尚未初始化）
        if (logRepository == null) {
            initialize(context)
        }

        if (logRepository != null) {
            coroutineScope.launch {
                try {
                    // 筛选出需要同步的新账号（在progressRecords中不存在的账号）
                    val newTokens = synchronized(progressRecordsLock) {
                        orderTokens.filter { token -> !progressRecords.containsKey(token.uid) }
                    }

                    if (newTokens.isEmpty()) {
                        Log.d("ProgressManager", "No new accounts to sync")
                        return@launch
                    }

                    Log.d("ProgressManager", "Syncing progress for ${newTokens.size} new accounts")

                    // 检查数据库状态
                    val dbHealthy = checkDatabaseHealth(context)
                    if (!dbHealthy) {
                        Log.w("ProgressManager", "Database health check failed, using fallback strategy for new accounts")
                        handleDatabaseUnavailable(newTokens)
                        return@launch
                    }

                    val allSyncedRecords = mutableMapOf<String, String>()

                    // 批处理参数
                    val totalTokens = newTokens.size
                    var processedCount = 0
                    var errorCount = 0
                    var timeoutCount = 0

                    // 分批处理令牌
                    val batches = newTokens.chunked(batchSize)
                    var shouldStop = false

                    for (batchIndex in batches.indices) {
                        if (shouldStop) break

                        val tokenBatch = batches[batchIndex]
                        try {
                            // 每批次的临时记录
                            val batchRecords = mutableMapOf<String, String>()

                            // 使用withTimeout确保每批处理不会超时
                            kotlinx.coroutines.withTimeoutOrNull(timeoutPerBatch) {
                                // 处理当前批次
                                tokenBatch.forEach { orderToken ->
                                    try {
                                        // 为单个查询添加超时保护
                                        val latestRecord = kotlinx.coroutines.withTimeoutOrNull(singleQueryTimeout) {
                                            logRepository?.getLatestLogByToken(orderToken.uid)
                                        }

                                        if (latestRecord != null) {
                                            batchRecords[orderToken.uid] = latestRecord.message
                                            allSyncedRecords[orderToken.uid] = latestRecord.message
                                            Log.d("ProgressManager", "Synced record for new token ${orderToken.uid}: ${latestRecord.message}")
                                        } else {
                                            // 查询超时或返回null，记录但不中断处理
                                            timeoutCount++
                                            Log.w("ProgressManager", "Query timeout or null result for new token ${orderToken.uid}")
                                        }
                                    } catch (e: Exception) {
                                        errorCount++
                                        Log.e("ProgressManager", "Error syncing record for new token ${orderToken.uid}", e)

                                        // 如果是数据库相关异常，考虑降级处理
                                        if (isDatabaseException(e)) {
                                            Log.w("ProgressManager", "Database exception detected, may need fallback strategy")
                                        }
                                    }
                                }
                            } ?: run {
                                // 批次超时处理
                                timeoutCount += tokenBatch.size
                                Log.w("ProgressManager", "New accounts batch ${batchIndex + 1} processing timed out after ${timeoutPerBatch}ms")
                            }

                            processedCount += tokenBatch.size
                            Log.d("ProgressManager", "Processed new accounts batch ${batchIndex + 1}: ${tokenBatch.size} tokens, total progress: $processedCount/$totalTokens")

                            // 每批处理完成后更新progressRecords和StateFlow
                            if (batchRecords.isNotEmpty()) {
                                val newMapSnapshot = synchronized(progressRecordsLock) {
                                    progressRecords.putAll(batchRecords)
                                    progressRecords.toMap()
                                }
                                _progressRecordsFlow.value = newMapSnapshot
                            }

                            // 添加延迟，避免过度消耗系统资源
                            if (batchIndex < batches.size - 1) {
                                kotlinx.coroutines.delay(delayBetweenBatches)
                            }

                            // 如果错误率过高，考虑中断处理
                            val errorRate = errorCount.toDouble() / processedCount
                            if (errorRate > 0.5 && processedCount > 20) {
                                Log.w("ProgressManager", "High error rate detected ($errorRate), stopping new accounts sync process")
                                shouldStop = true
                            }
                        } catch (e: Exception) {
                            Log.e("ProgressManager", "Error processing new accounts batch ${batchIndex + 1}", e)
                        }
                    }

                    Log.d("ProgressManager", "Synced ${allSyncedRecords.size} new account records from database with $errorCount errors and $timeoutCount timeouts")

                    // 如果同步成功率很低，触发数据库维护
                    val successRate = allSyncedRecords.size.toDouble() / totalTokens
                    if (successRate < 0.3 && totalTokens > 10) {
                        Log.w("ProgressManager", "Low sync success rate for new accounts ($successRate), triggering database maintenance")
                        triggerDatabaseMaintenance(context)
                    }
                } catch (e: Exception) {
                    Log.e("ProgressManager", "Failed to sync new account records from database", e)
                    // 发生严重错误时，尝试降级处理
                    val newTokens = synchronized(progressRecordsLock) {
                        orderTokens.filter { token -> !progressRecords.containsKey(token.uid) }
                    }
                    handleDatabaseUnavailable(newTokens)
                }
            }
        } else {
            Log.w("ProgressManager", "LogRepository is null, cannot sync new account progress")
        }
    }

    /**
     * 检查数据库健康状态
     * @param context 应用上下文
     * @return 数据库是否健康
     */
    private suspend fun checkDatabaseHealth(context: Context): Boolean {
        return try {
            val healthInfo = DatabaseMaintenanceUtils.checkDatabaseHealth(context)

            if (!healthInfo.isHealthy) {
                Log.w("ProgressManager", "Database health issues: ${healthInfo.issues}")

                // 如果数据库大小过大，触发自动维护
                if (healthInfo.databaseSizeMB > 50) {
                    Log.w("ProgressManager", "Database size is large (${healthInfo.databaseSizeMB}MB), triggering maintenance")
                    DatabaseMaintenanceUtils.performAutoMaintenance(context)
                }
            }

            healthInfo.isHealthy
        } catch (e: Exception) {
            Log.e("ProgressManager", "Database health check failed", e)
            false
        }
    }

    /**
     * 判断是否为数据库相关异常
     * @param exception 异常对象
     * @return 是否为数据库异常
     */
    private fun isDatabaseException(exception: Exception): Boolean {
        val message = exception.message?.lowercase() ?: ""
        return message.contains("database") ||
               message.contains("sqlite") ||
               message.contains("cursor") ||
               message.contains("timeout") ||
               exception is android.database.SQLException ||
               exception is kotlinx.coroutines.TimeoutCancellationException
    }

    /**
     * 处理数据库不可用的情况
     * @param orderTokens 令牌列表
     */
    private fun handleDatabaseUnavailable(orderTokens: List<OrderTokenEntity>) {
        Log.w("ProgressManager", "Database unavailable, using fallback strategy for ${orderTokens.size} tokens")

        // 为所有令牌设置默认的进度记录
        val fallbackRecords = mutableMapOf<String, String>()
        orderTokens.forEach { token ->
            fallbackRecords[token.uid] = "数据库暂时不可用，请稍后查看进度"
        }

        // 更新进度记录
        val newMapSnapshot = synchronized(progressRecordsLock) {
            progressRecords.putAll(fallbackRecords)
            progressRecords.toMap()
        }
        _progressRecordsFlow.value = newMapSnapshot

        Log.d("ProgressManager", "Applied fallback records for ${fallbackRecords.size} tokens")
    }

    /**
     * 触发数据库维护
     * @param context 应用上下文
     */
    private fun triggerDatabaseMaintenance(context: Context) {
        Log.d("ProgressManager", "Triggering database maintenance due to low sync success rate")
        DatabaseMaintenanceUtils.performAutoMaintenance(context)
    }

    /**
     * 清除指定tokenUid的进度记录。
     * @param tokenUid 令牌的唯一标识符。
     * @param deleteFromDb 是否从数据库中删除，默认为false
     */
    fun clearRecord(tokenUid: String, deleteFromDb: Boolean = false) {
        val newMapSnapshot = synchronized(progressRecordsLock) {
            progressRecords.remove(tokenUid)
            progressRecords.toMap()
        }
        _progressRecordsFlow.value = newMapSnapshot

        // 从数据库删除
        if (deleteFromDb && logRepository != null) {
            coroutineScope.launch {
                try {
                    logRepository?.deleteLogsByToken(tokenUid)
                    Log.d("ProgressManager", "Logs deleted from database successfully")
                } catch (e: Exception) {
                    Log.e("ProgressManager", "Failed to delete logs from database", e)
                }
            }
        }
    }

    /**
     * 清除所有进度记录。
     * @param clearDb 是否清除数据库，默认为false
     */
    fun clearAllRecords(clearDb: Boolean = false) {
        val newMapSnapshot = synchronized(progressRecordsLock) {
            progressRecords.clear()
            progressRecords.toMap()
        }
        _progressRecordsFlow.value = newMapSnapshot

        // 清空数据库
        if (clearDb && logRepository != null) {
            coroutineScope.launch {
                try {
                    logRepository?.clearAllLogs()
                    Log.d("ProgressManager", "All logs cleared from database successfully")
                } catch (e: Exception) {
                    Log.e("ProgressManager", "Failed to clear logs from database", e)
                }
            }
        }
    }
}

/**
 * 进度记录器类
 * 用于记录处理过程并保存在内存中的临时记录
 * @param tokenUid 与此记录器关联的令牌的唯一标识符。
 * @param phoneNumber 关联的手机号码，用于标识用户。
 * @param tag 用于日志记录的日志标签。默认为"ProcessRecorder"。
 * @param logLevelDefault 日志级别。默认为"INFO"。
 * @param saveToDb 是否保存到数据库。默认为true。
 * @param context 应用上下文，用于数据库初始化。
 */
class ProcessRecorder(
    private val tokenUid: String,
    private val phoneNumber: String,
    private val tag: String = "ProcessRecorder",
    private val logLevelDefault: String = "INFO",
    private val saveToDb: Boolean = true,
    context: Context? = null
) {
    private val dateFormatter = SimpleDateFormat("HH:mm:ss", Locale.CHINA)
    private val currentTime: String
        get() = dateFormatter.format(Date())
    
    // 当前记录器的操作序列计数
    private var operationSequence = 0L

    init {
        // 初始化日志仓库，如果提供了上下文
        context?.let {
            ProgressManager.initialize(it.applicationContext)
        }
    }

    /**
     * 记录处理步骤
     * @param message 步骤描述
     * @param logLevel 日志级别
     */
    fun recordProcess(message: String, logLevel: String = logLevelDefault) {
        // 获取下一个序列号
        val sequenceNumber = ProgressManager.getNextSequenceNumber(tokenUid)
        
        // 格式化消息，添加序列号信息（但不显示在UI上）
        val formattedMessage = "$currentTime $message"
        
        Log.d(tag, "Token $tokenUid, Phone $phoneNumber: $formattedMessage (seq: $sequenceNumber)")
        ProgressManager.updateRecord(tokenUid, formattedMessage, tag, logLevel, saveToDb, phoneNumber, sequenceNumber)
    }
} 